import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Loader2, RefreshCw, File<PERSON>son, Check, Copy, AlertTriangle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import Editor from '@monaco-editor/react';

interface SchemaData {
  id: string;
  page_url: string;
  schema_type: 'BlogPosting' | 'TrekPackage' | 'FAQPage' | 'LocalBusiness';
  schema_data: Record<string, unknown>;
  last_validated: string | null;
  validation_errors: string[];
  auto_update: boolean;
  created_at: string;
}

const SchemaManager: React.FC = () => {
  const [schemas, setSchemas] = useState<SchemaData[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState<string | null>(null);
  const [selectedSchema, setSelectedSchema] = useState<SchemaData | null>(null);
  const [copied, setCopied] = useState(false);

  const fetchSchemas = async () => {
    try {
      const { data, error } = await supabase
        .from('schema_registry')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSchemas(data || []);
    } catch (error) {
      console.error('Error fetching schemas:', error);
      toast({
        title: "Error",
        description: "Failed to fetch schemas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSchemas();
  }, []);

  const generateSchema = async (type: string, id: string) => {
    setGenerating(id);
    try {
      const baseUrl = window.location.origin;
      const { data, error } = await supabase.functions.invoke('schema-generator', {
        body: { type, id, baseUrl }
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Schema generated successfully",
      });

      fetchSchemas();
    } catch (error) {
      console.error('Error generating schema:', error);
      toast({
        title: "Error",
        description: "Failed to generate schema",
        variant: "destructive",
      });
    } finally {
      setGenerating(null);
    }
  };

  const copyToClipboard = async (schema: Record<string, unknown>) => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(schema, null, 2));
      setCopied(true);
      toast({
        title: "Copied",
        description: "Schema copied to clipboard",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
    }
  };

  const formatDate = (date: string | null) => {
    if (!date) return 'Never';
    return new Date(date).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Schema Manager</h2>
        <Button
          variant="outline"
          onClick={() => fetchSchemas()}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>JSON-LD Schemas</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Page URL</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Validated</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {schemas.map((schema) => (
                <TableRow key={schema.id}>
                  <TableCell className="max-w-xs truncate">
                    {schema.page_url}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{schema.schema_type}</Badge>
                  </TableCell>
                  <TableCell>
                    {schema.validation_errors.length === 0 ? (
                      <Badge className="bg-green-100 text-green-800">
                        <Check className="h-3 w-3 mr-1" />
                        Valid
                      </Badge>
                    ) : (
                      <Badge className="bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {schema.validation_errors.length} Issues
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-sm text-gray-500">
                    {formatDate(schema.last_validated)}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <FileJson className="h-4 w-4 mr-2" />
                            View Schema
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
                          <DialogHeader>
                            <DialogTitle>Schema Details</DialogTitle>
                            <DialogDescription>
                              {schema.page_url}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            {schema.validation_errors.length > 0 && (
                              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                <h4 className="font-semibold text-yellow-800 mb-2">Validation Issues</h4>
                                <ul className="list-disc list-inside space-y-1">
                                  {schema.validation_errors.map((error, index) => (
                                    <li key={index} className="text-yellow-700 text-sm">
                                      {error}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            <div className="flex justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(schema.schema_data)}
                              >
                                {copied ? (
                                  <Check className="h-4 w-4 mr-2" />
                                ) : (
                                  <Copy className="h-4 w-4 mr-2" />
                                )}
                                Copy Schema
                              </Button>
                            </div>
                            <div className="border rounded-lg overflow-hidden">
                              <Editor
                                height="400px"
                                defaultLanguage="json"
                                defaultValue={JSON.stringify(schema.schema_data, null, 2)}
                                options={{
                                  readOnly: true,
                                  minimap: { enabled: false },
                                  scrollBeyondLastLine: false,
                                }}
                              />
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button
                        size="sm"
                        disabled={generating === schema.id}
                        onClick={() => {
                          const type = schema.schema_type === 'BlogPosting' ? 'blog' : 'trek';
                          const contentId = new URL(schema.page_url).pathname.split('/').pop() || '';
                          generateSchema(type, contentId);
                        }}
                      >
                        {generating === schema.id && (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        )}
                        Regenerate
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default SchemaManager;
