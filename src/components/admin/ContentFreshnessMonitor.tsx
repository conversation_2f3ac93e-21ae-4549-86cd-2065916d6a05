import React, { useState, useEffect } from 'react';
import { format, parseISO } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Loader2, RefreshCw, CalendarClock, AlertTriangle, CheckCircle2 } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { useContentFreshness } from '@/hooks/use-content-freshness';
import type { ContentFreshnessData } from '@/types/seo';

interface ContentDetails {
  title: string;
  url: string;
}

const ContentFreshnessMonitor: React.FC = () => {
  const [content, setContent] = useState<(ContentFreshnessData & ContentDetails)[]>([]);
  const { batchAnalyzeFreshness, loading, error } = useContentFreshness();
  const [analyzing, setAnalyzing] = useState(false);

  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    }
  }, [error]);

  const fetchContent = async () => {
    try {
      const freshnessData = await batchAnalyzeFreshness('blog');
      const trekFreshnessData = await batchAnalyzeFreshness('trek');
      const allFreshnessData = [...freshnessData, ...trekFreshnessData];

      // Fetch content details
      const enrichedContent = await Promise.all(allFreshnessData.map(async (item) => {
        if (item.content_type === 'blog') {
          const { data: blog } = await supabase
            .from('blog_posts')
            .select('title, slug')
            .eq('id', item.content_id)
            .single();

          return {
            ...item,
            title: blog?.title || 'Unknown Blog',
            url: `/blog/${blog?.slug}`
          };
        } else {
          const { data: trek } = await supabase
            .from('trek_packages')
            .select('name, slug')
            .eq('id', item.content_id)
            .single();

          return {
            ...item,
            title: trek?.name || 'Unknown Trek',
            url: `/treks/${trek?.slug}`
          };
        }
      }));

      setContent(enrichedContent);
    } catch (error) {
      console.error('Error fetching content freshness:', error);
      toast({
        title: "Error",
        description: "Failed to fetch content freshness data",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    fetchContent();
  }, []);

  const analyzeContent = async () => {
    setAnalyzing(true);
    try {
      await Promise.all([
        batchAnalyzeFreshness('blog'),
        batchAnalyzeFreshness('trek')
      ]);
      
      toast({
        title: "Success",
        description: "Content freshness analysis completed",
      });

      fetchContent();
    } catch (error) {
      console.error('Error analyzing content:', error);
      toast({
        title: "Error",
        description: "Failed to analyze content",
        variant: "destructive",
      });
    } finally {
      setAnalyzing(false);
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge className="bg-red-100 text-red-800">High Priority</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Medium Priority</Badge>;
      default:
        return <Badge className="bg-green-100 text-green-800">Low Priority</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return format(parseISO(dateString), 'MMM d, yyyy');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Content Freshness</h2>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => fetchContent()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            onClick={analyzeContent}
            disabled={analyzing}
          >
            <CalendarClock className="h-4 w-4 mr-2" />
            {analyzing ? 'Analyzing...' : 'Analyze Content'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Average Freshness Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(content.reduce((acc, item) => acc + item.freshness_score, 0) / content.length * 100)}%
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Content Needing Updates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {content.filter(item => item.update_priority !== 'low').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Seasonal Content
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {content.filter(item => item.seasonal_relevance).length}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Content Status</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Content</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Freshness</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Next Review</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {content.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">
                    <div className="max-w-xs truncate">{item.title}</div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {item.content_type === 'blog' ? 'Blog Post' : 'Trek Package'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="w-full space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{Math.round(item.freshness_score * 100)}%</span>
                      </div>
                      <Progress
                        value={item.freshness_score * 100}
                        className={`h-2 ${
                          item.freshness_score > 0.7 
                            ? 'bg-green-100' 
                            : item.freshness_score > 0.4 
                              ? 'bg-yellow-100' 
                              : 'bg-red-100'
                        }`}
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    {getPriorityBadge(item.update_priority)}
                  </TableCell>
                  <TableCell>
                    {formatDate(item.next_review_date)}
                  </TableCell>
                  <TableCell>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">View Details</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Content Freshness Details</DialogTitle>
                          <DialogDescription>
                            {item.title}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="text-sm font-medium">Last Updated</label>
                              <p className="text-sm text-gray-500">
                                {formatDate(item.last_updated)}
                              </p>
                            </div>
                            <div>
                              <label className="text-sm font-medium">Next Review</label>
                              <p className="text-sm text-gray-500">
                                {formatDate(item.next_review_date)}
                              </p>
                            </div>
                          </div>
                          
                          <div>
                            <label className="text-sm font-medium">Seasonal Relevance</label>
                            <div className="mt-1">
                              {item.seasonal_relevance ? (
                                <Badge className="bg-green-100 text-green-800">
                                  <CheckCircle2 className="h-3 w-3 mr-1" />
                                  Seasonally Relevant
                                </Badge>
                              ) : (
                                <Badge className="bg-yellow-100 text-yellow-800">
                                  <AlertTriangle className="h-3 w-3 mr-1" />
                                  Not Seasonally Relevant
                                </Badge>
                              )}
                            </div>
                          </div>

                          <div>
                            <label className="text-sm font-medium">Suggested Updates</label>
                            <div className="mt-2 space-y-3">
                              {item.suggested_updates.map((update, index) => (
                                <div key={index} className="rounded-lg border p-3">
                                  <h4 className="font-medium mb-1">{update.section}</h4>
                                  <p className="text-sm text-yellow-800 bg-yellow-50 p-2 rounded mb-2">
                                    {update.reason}
                                  </p>
                                  <p className="text-sm text-green-800 bg-green-50 p-2 rounded">
                                    {update.suggestion}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContentFreshnessMonitor;
