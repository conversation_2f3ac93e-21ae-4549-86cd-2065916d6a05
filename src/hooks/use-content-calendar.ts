import { useCallback } from 'react';
import { supabase, callFunction } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

import { ContentSuggestion } from '@/types/seo';

/**
 * Core service functions (no React hooks here)
 */
export async function generateSuggestions(): Promise<ContentSuggestion[] | null> {
  try {
    const data = await callFunction<{ suggestions: ContentSuggestion[]; suggestions_count: number }>(
      'content-calendar-generator',
      { mode: 'generate' }
    );

    return data.suggestions as ContentSuggestion[];
  } catch (error) {
    console.error('Error generating suggestions:', error);
    return null;
  }
}

export async function getCalendarStats() {
  try {
    const { data, error } = await supabase
      .from('content_calendar')
      .select('*');

    if (error) throw error;

    const totalSuggestions = data.length;
    const approved = data.filter(item => item.status === 'approved').length;
    const published = data.filter(item => item.status === 'published').length;
    const pending = data.filter(item => item.status === 'suggested').length;

    const averageSeasonality = data.reduce((acc, item) => acc + item.seasonality_score, 0) / totalSuggestions;
    const averageEngagement = data.reduce((acc, item) => acc + item.predicted_engagement, 0) / totalSuggestions;

    return {
      totalSuggestions,
      approved,
      published,
      pending,
      averageSeasonality,
      averageEngagement,
    };
  } catch (error) {
    console.error('Error fetching calendar stats:', error);
    return null;
  }
}

export async function updateSuggestionStatus(
  id: string, 
  status: 'approved' | 'published'
): Promise<void> {
  try {
    const { error } = await supabase
      .from('content_calendar')
      .update({ status })
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    console.error('Error updating suggestion status:', error);
    // Don't throw here, let the hook wrapper handle the error
  }
}

export async function getSuggestionsForMonth(month: number, year: number): Promise<ContentSuggestion[] | null> {
  try {
    const startDate = new Date(year, month, 1).toISOString();
    const endDate = new Date(year, month + 1, 0).toISOString();

    const { data, error } = await supabase
      .from('content_calendar')
      .select('*')
      .gte('suggested_date', startDate)
      .lte('suggested_date', endDate)
      .order('suggested_date', { ascending: true });

    if (error) throw error;
    return data as ContentSuggestion[];
  } catch (error) {
    console.error('Error fetching monthly suggestions:', error);
    return null;
  }
}

/**
 * Thin hook wrapper — pure linter convenience in React components,
 * but _tests_ will never hit a `useState` or `useCallback`.
 */
export const useContentCalendar = () => {
  const wrappedGenerateSuggestions = useCallback(async () => {
    try {
      const result = await generateSuggestions();
      if (result) {
        toast({
          title: "Success",
          description: `Generated ${result.length} content suggestions`,
        });
      }
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate content suggestions",
        variant: "destructive",
      });
      return null;
    }
  }, []);

  const wrappedGetCalendarStats = useCallback(async () => {
    return await getCalendarStats();
  }, []);

  const wrappedUpdateSuggestionStatus = useCallback(async (
    id: string, 
    status: 'approved' | 'published'
  ) => {
    try {
      await updateSuggestionStatus(id, status);
      toast({
        title: "Status Updated",
        description: `Content suggestion marked as ${status}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update status",
        variant: "destructive",
      });
    }
  }, []);

  const wrappedGetSuggestionsForMonth = useCallback(async (month: number, year: number) => {
    return await getSuggestionsForMonth(month, year);
  }, []);

  return {
    generateSuggestions: wrappedGenerateSuggestions,
    getCalendarStats: wrappedGetCalendarStats,
    updateSuggestionStatus: wrappedUpdateSuggestionStatus,
    getSuggestionsForMonth: wrappedGetSuggestionsForMonth,
  };
};
