import { useEffect, useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Loader2, RefreshCw, Calendar as CalendarIcon } from 'lucide-react';
import { useContentCalendar } from '@/hooks/use-content-calendar';
import { ContentSuggestion } from '@/types/seo';

export default function ContentCalendar() {
  const [loading, setLoading] = useState(false);
  const [date, setDate] = useState<Date>(new Date());
  const [suggestions, setSuggestions] = useState<ContentSuggestion[]>([]);
  const { generateSuggestions, getSuggestionsForMonth, updateSuggestionStatus } = useContentCalendar();

  const loadSuggestions = async () => {
    setLoading(true);
    try {
      const data = await getSuggestionsForMonth(date.getMonth(), date.getFullYear());
      if (data) {
        setSuggestions(data);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateSuggestions = async () => {
    setLoading(true);
    try {
      const newSuggestions = await generateSuggestions();
      if (newSuggestions) {
        setSuggestions(prev => [...prev, ...newSuggestions]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (id: string, status: 'approved' | 'published') => {
    await updateSuggestionStatus(id, status);
    // Refresh suggestions after status update
    loadSuggestions();
  };

  useEffect(() => {
    loadSuggestions();
  }, [date]);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Content Calendar</h2>
        <Button onClick={handleGenerateSuggestions} disabled={loading}>
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Generate Suggestions
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>Select Month</CardTitle>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              selected={date}
              onSelect={(newDate) => newDate && setDate(newDate)}
              className="rounded-md border"
            />
          </CardContent>
        </Card>

        <Card className="col-span-2">
          <CardHeader>
            <CardTitle>Content Suggestions</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Topic</TableHead>
                  <TableHead>Seasonality</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {suggestions.map((suggestion) => (
                  <TableRow key={suggestion.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{suggestion.topic}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {suggestion.keywords.map((keyword, i) => (
                            <Badge key={i} variant="secondary">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={
                        suggestion.seasonality_score > 0.7 ? "destructive" :
                        suggestion.seasonality_score > 0.4 ? "default" : "secondary"
                      }>
                        {Math.round(suggestion.seasonality_score * 100)}%
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={
                        suggestion.status === 'published' ? "default" :
                        suggestion.status === 'approved' ? "default" : "secondary"
                      }>
                        {suggestion.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {suggestion.status === 'suggested' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusUpdate(suggestion.id, 'approved')}
                          >
                            Approve
                          </Button>
                        )}
                        {suggestion.status === 'approved' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusUpdate(suggestion.id, 'published')}
                          >
                            Mark Published
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {suggestions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-6 text-muted-foreground">
                      No suggestions found for this month.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
