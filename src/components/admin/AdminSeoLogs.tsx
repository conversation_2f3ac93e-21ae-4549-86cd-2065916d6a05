import React, { useState, useEffect } from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw, AlertTriangle, CheckCircle2, Sparkles, Code2, Calendar } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useContentFreshness } from '@/hooks/use-content-freshness';
import { useContentCalendar } from '@/hooks/use-content-calendar';
import { useSchemaGenerator } from '@/hooks/use-schema-generator';
import { useEnhancedSEOOptimizer } from '@/hooks/use-enhanced-seo-optimizer';
import { contentVersioningManager } from '@/lib/seo/content-versioning';
import { ContentFreshnessData, SchemaRegistryEntry } from '@/types/seo';

interface SEOMetrics {
  contentFreshness: {
    averageFreshness: number;
    needsUpdate: number;
    seasonalContent: ContentFreshnessData[];
  };
  schemas: {
    coverage: number;
    validSchemas: number;
    totalSchemas: number;
  };
  contentCalendar: {
    totalSuggestions: number;
    approved: number;
    published: number;
  };
  enhancedSEO: {
    totalOptimizations: number;
    successRate: number;
    averageProcessingTime: number;
    systemHealth: string;
    totalVersions: number;
    averageOptimizationGain: number;
  };
}

const AdminSeoLogs: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<SEOMetrics | null>(null);
  const [recentUpdates, setRecentUpdates] = useState<Array<ContentFreshnessData & { type: string }>>([]);
  const [schemaIssues, setSchemaIssues] = useState<SchemaRegistryEntry[]>([]);
  const { getFreshnessStats } = useContentFreshness();
  const { getCalendarStats } = useContentCalendar();
  const { getSchemaRegistry } = useSchemaGenerator();
  const { stats: enhancedSEOStats, health: seoHealth, successRate, averageProcessingTime } = useEnhancedSEOOptimizer();

  const fetchAllMetrics = async () => {
    setLoading(true);
    try {
      const [freshness, calendar, schemas] = await Promise.all([
        getFreshnessStats(),
        getCalendarStats(),
        getSchemaRegistry()
      ]);

      // Get enhanced SEO stats
      const versioningStats = contentVersioningManager.getStats();

      if (freshness && calendar && schemas) {
        setMetrics({
          contentFreshness: {
            averageFreshness: freshness.averageFreshness * 100,
            needsUpdate: freshness.needsUpdate,
            seasonalContent: freshness.seasonalContent
          },
          schemas: {
            coverage: (schemas.filter(s => s.validation_errors.length === 0).length / schemas.length) * 100,
            validSchemas: schemas.filter(s => s.validation_errors.length === 0).length,
            totalSchemas: schemas.length
          },
          contentCalendar: {
            totalSuggestions: calendar.totalSuggestions,
            approved: calendar.approved,
            published: calendar.published
          },
          enhancedSEO: {
            totalOptimizations: enhancedSEOStats?.totalRequests || 0,
            successRate: successRate || 0,
            averageProcessingTime: averageProcessingTime || 0,
            systemHealth: seoHealth?.status || 'unknown',
            totalVersions: versioningStats.totalVersions,
            averageOptimizationGain: versioningStats.averageOptimizationGain
          }
        });

        // Set recent updates, schema issues and pending optimizations
        setRecentUpdates(freshness.priorityUpdates.map(update => ({ ...update, type: 'Content Update' })));
        setSchemaIssues(schemas.filter(s => s.validation_errors.length > 0));
      }
    } catch (error) {
      console.error('Error fetching SEO metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAllMetrics();
  }, []);

  if (loading || !metrics) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">SEO Overview</h2>
        <Button variant="outline" onClick={fetchAllMetrics} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Content Freshness</CardTitle>
            <Sparkles className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.contentFreshness.averageFreshness.toFixed(1)}%</div>
            <Progress value={metrics.contentFreshness.averageFreshness} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {metrics.contentFreshness.needsUpdate} items need updates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Schema Coverage</CardTitle>
            <Code2 className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.schemas.coverage.toFixed(1)}%</div>
            <Progress value={metrics.schemas.coverage} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {metrics.schemas.validSchemas} of {metrics.schemas.totalSchemas} schemas valid
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Content Calendar</CardTitle>
            <Calendar className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.contentCalendar.totalSuggestions}</div>
            <p className="text-xs text-muted-foreground mt-2">
              {metrics.contentCalendar.published} published, {metrics.contentCalendar.approved} approved
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Enhanced SEO</CardTitle>
            <Sparkles className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.enhancedSEO.successRate.toFixed(1)}%</div>
            <Progress value={metrics.enhancedSEO.successRate} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {metrics.enhancedSEO.totalOptimizations} optimizations, {metrics.enhancedSEO.systemHealth} status
            </p>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={metrics.enhancedSEO.systemHealth === 'healthy' ? 'default' : 'destructive'}>
                {metrics.enhancedSEO.systemHealth}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {metrics.enhancedSEO.averageProcessingTime.toFixed(0)}ms avg
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Content Updates Needed</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Next Review</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentUpdates.map((update, i) => (
                  <TableRow key={i}>
                    <TableCell>{update.content_type}</TableCell>
                    <TableCell>
                      <Badge variant={update.update_priority === 'high' ? 'destructive' : 'default'}>
                        {update.update_priority}
                      </Badge>
                    </TableCell>
                    <TableCell>{new Date(update.next_review_date).toLocaleDateString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Schema Validation Issues</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Page</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Issues</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {schemaIssues.map((schema, i) => (
                  <TableRow key={i}>
                    <TableCell className="max-w-[200px] truncate">
                      {schema.page_url}
                    </TableCell>
                    <TableCell>{schema.schema_type}</TableCell>
                    <TableCell>
                      <Badge variant="destructive">
                        {schema.validation_errors.length} issues
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminSeoLogs;
