/**
 * SEO Monitoring Dashboard
 * Comprehensive monitoring and analytics for SEO optimization system
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Clock,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  PieChart,
  RefreshCw,
  Download,
  Settings,
  Eye,
  Target,
  Globe
} from 'lucide-react';
import { useEnhancedSEOOptimizer } from '@/hooks/use-enhanced-seo-optimizer';
import { contentVersioningManager, VersioningStats } from '@/lib/seo/content-versioning';

interface PerformanceMetric {
  name: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  unit: string;
}

interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  timestamp: Date;
  resolved: boolean;
}

export default function SEOMonitoringDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [versioningStats, setVersioningStats] = useState<VersioningStats | null>(null);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  const {
    stats,
    health,
    checkHealth,
    resetStats,
    isHealthy,
    isDegraded,
    isUnhealthy,
    successRate,
    averageProcessingTime
  } = useEnhancedSEOOptimizer();

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setVersioningStats(contentVersioningManager.getStats());
      await checkHealth();
      generateSystemAlerts();
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setTimeout(() => setRefreshing(false), 1000);
  };

  const generateSystemAlerts = () => {
    const alerts: SystemAlert[] = [];

    if (stats) {
      // High failure rate alert
      if (stats.failedOptimizations / stats.totalRequests > 0.1) {
        alerts.push({
          id: 'high-failure-rate',
          type: 'warning',
          message: `High failure rate detected: ${((stats.failedOptimizations / stats.totalRequests) * 100).toFixed(1)}%`,
          timestamp: new Date(),
          resolved: false
        });
      }

      // Slow processing alert
      if (averageProcessingTime > 5000) {
        alerts.push({
          id: 'slow-processing',
          type: 'warning',
          message: `Slow processing detected: ${averageProcessingTime.toFixed(0)}ms average`,
          timestamp: new Date(),
          resolved: false
        });
      }

      // Circuit breaker alert
      if (stats.circuitBreakerStats.state === 'OPEN') {
        alerts.push({
          id: 'circuit-breaker-open',
          type: 'error',
          message: 'Circuit breaker is open - service degraded',
          timestamp: new Date(),
          resolved: false
        });
      }

      // Rate limit queue alert
      if (stats.rateLimitStats.queueLength > 40) {
        alerts.push({
          id: 'rate-limit-queue-full',
          type: 'warning',
          message: `Rate limit queue is nearly full: ${stats.rateLimitStats.queueLength}/50`,
          timestamp: new Date(),
          resolved: false
        });
      }
    }

    setSystemAlerts(alerts);
  };

  const performanceMetrics: PerformanceMetric[] = [
    {
      name: 'Success Rate',
      value: successRate,
      change: 2.3,
      trend: 'up',
      unit: '%'
    },
    {
      name: 'Avg Processing Time',
      value: averageProcessingTime,
      change: -150,
      trend: 'down',
      unit: 'ms'
    },
    {
      name: 'Total Optimizations',
      value: stats?.totalRequests || 0,
      change: 12,
      trend: 'up',
      unit: ''
    },
    {
      name: 'Active Versions',
      value: versioningStats?.activeVersions || 0,
      change: 3,
      trend: 'up',
      unit: ''
    }
  ];

  const getHealthStatusIcon = () => {
    if (isHealthy) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (isDegraded) return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    if (isUnhealthy) return <XCircle className="h-5 w-5 text-red-500" />;
    return <Activity className="h-5 w-5 text-gray-500" />;
  };

  const getHealthStatusText = () => {
    if (isHealthy) return 'Healthy';
    if (isDegraded) return 'Degraded';
    if (isUnhealthy) return 'Unhealthy';
    return 'Unknown';
  };

  const getHealthStatusColor = () => {
    if (isHealthy) return 'bg-green-100 text-green-800 border-green-200';
    if (isDegraded) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (isUnhealthy) return 'bg-red-100 text-red-800 border-red-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const exportData = () => {
    const data = {
      stats,
      health,
      versioningStats,
      systemAlerts,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `seo-monitoring-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">SEO Monitoring Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time monitoring and analytics for SEO optimization system
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">System Health</p>
                <Badge className={getHealthStatusColor()}>
                  {getHealthStatusText()}
                </Badge>
              </div>
              {getHealthStatusIcon()}
            </div>
          </CardContent>
        </Card>

        {performanceMetrics.map((metric, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{metric.name}</p>
                  <p className="text-2xl font-bold">
                    {metric.value.toFixed(metric.unit === '%' ? 1 : 0)}{metric.unit}
                  </p>
                  <div className="flex items-center gap-1 text-sm">
                    {getTrendIcon(metric.trend)}
                    <span className={metric.trend === 'up' ? 'text-green-600' : metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'}>
                      {metric.change > 0 ? '+' : ''}{metric.change}{metric.unit}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* System Alerts */}
      {systemAlerts.length > 0 && (
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">System Alerts</h2>
          {systemAlerts.map((alert) => (
            <Alert key={alert.id} variant={alert.type === 'error' ? 'destructive' : 'default'}>
              {alert.type === 'error' ? (
                <XCircle className="h-4 w-4" />
              ) : alert.type === 'warning' ? (
                <AlertTriangle className="h-4 w-4" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {alert.message}
                <span className="text-xs text-muted-foreground ml-2">
                  {alert.timestamp.toLocaleTimeString()}
                </span>
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}

      {/* Detailed Monitoring */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="versioning">Versioning</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Optimization Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Success Rate</span>
                      <span className="font-bold">{successRate.toFixed(1)}%</span>
                    </div>
                    <Progress value={successRate} className="h-2" />
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Successful</p>
                        <p className="font-bold text-green-600">{stats.successfulOptimizations}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Failed</p>
                        <p className="font-bold text-red-600">{stats.failedOptimizations}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Processing Times
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Average Time</span>
                    <span className="font-bold">{averageProcessingTime.toFixed(0)}ms</span>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Fast (&lt;1s)</span>
                      <span className="text-green-600">85%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Medium (1-3s)</span>
                      <span className="text-yellow-600">12%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Slow (&gt;3s)</span>
                      <span className="text-red-600">3%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {versioningStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Content Optimization Impact
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      +{versioningStats.averageOptimizationGain.toFixed(1)}
                    </p>
                    <p className="text-sm text-muted-foreground">Avg SEO Score Gain</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{versioningStats.totalVersions}</p>
                    <p className="text-sm text-muted-foreground">Total Versions</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{versioningStats.activeVersions}</p>
                    <p className="text-sm text-muted-foreground">Active Content</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Circuit Breaker</CardTitle>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>State</span>
                      <Badge variant={stats.circuitBreakerStats.state === 'CLOSED' ? 'default' : 'destructive'}>
                        {stats.circuitBreakerStats.state}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Failures</span>
                      <span>{stats.circuitBreakerStats.failureCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Successes</span>
                      <span>{stats.circuitBreakerStats.successCount}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Rate Limiting</CardTitle>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Available Tokens</span>
                      <span>{stats.rateLimitStats.currentTokens}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Queue Length</span>
                      <span>{stats.rateLimitStats.queueLength}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg Wait Time</span>
                      <span>{stats.rateLimitStats.averageWaitTime.toFixed(0)}ms</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Retry Handler</CardTitle>
              </CardHeader>
              <CardContent>
                {stats && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Total Attempts</span>
                      <span>{stats.retryStats.totalAttempts}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Successful Retries</span>
                      <span>{stats.retryStats.successfulRetries}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Failed Retries</span>
                      <span>{stats.retryStats.failedRetries}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="versioning" className="space-y-4">
          {versioningStats && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Most Improved Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-48">
                      <div className="space-y-2">
                        {versioningStats.mostImprovedContent.map((version, index) => (
                          <div key={version.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <p className="font-medium text-sm">{version.title}</p>
                              <p className="text-xs text-muted-foreground">
                                Score: {version.seoScore}/10
                              </p>
                            </div>
                            <Badge variant="outline">#{index + 1}</Badge>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Recent Optimizations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-48">
                      <div className="space-y-2">
                        {versioningStats.recentOptimizations.map((version) => (
                          <div key={version.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <p className="font-medium text-sm">{version.title}</p>
                              <p className="text-xs text-muted-foreground">
                                {version.createdAt.toLocaleDateString()}
                              </p>
                            </div>
                            <Badge variant="outline">
                              {version.optimizationData.optimizationType}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>System Health Details</CardTitle>
              </CardHeader>
              <CardContent>
                {health && (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Status</span>
                      <Badge className={getHealthStatusColor()}>
                        {health.status}
                      </Badge>
                    </div>
                    <div className="mt-4">
                      <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                        {JSON.stringify(health.details, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button onClick={checkHealth} className="w-full">
                  <Activity className="h-4 w-4 mr-2" />
                  Check Health
                </Button>
                <Button onClick={resetStats} variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reset Statistics
                </Button>
                <Button onClick={exportData} variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="text-center py-8 text-muted-foreground">
            <BarChart3 className="h-12 w-12 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Advanced Analytics</h3>
            <p>Detailed analytics and reporting features coming soon</p>
            <p className="text-sm">Will include traffic impact analysis, keyword performance, and ROI metrics</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
