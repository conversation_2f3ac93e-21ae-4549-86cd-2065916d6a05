import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Clock, Users, TrendingUp, MapPin, Star, Calendar, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { TrekPackage } from '@/types/database';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import WhatsAppButton from '@/components/WhatsAppButton';
import BookingModal from '@/components/BookingModal';

const TrekDetail = () => {
  const { slug } = useParams<{ slug: string }>();
  const [trek, setTrek] = useState<TrekPackage | null>(null);
  const [loading, setLoading] = useState(true);
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState<number | null>(null);

  useEffect(() => {
    if (slug) {
      fetchTrek();
    }
  }, [slug]);

  const fetchTrek = async () => {
    try {
      const { data, error } = await supabase
        .from('trek_packages')
        .select('*')
        .eq('slug', slug)
        .single<TrekPackage>();

      if (error) throw error;
      
      const transformedTrek = {
        ...data,
        itinerary: Array.isArray(data.itinerary) ? data.itinerary : [],
        duration_variations: data.duration_variations || { variations: [] }
      } as TrekPackage;
      
      setTrek(transformedTrek);
      // Set initial selected duration to minimum days or first variation
      if (transformedTrek.minimum_days) {
        setSelectedDuration(transformedTrek.minimum_days);
      } else if (transformedTrek.duration_variations.variations.length > 0) {
        setSelectedDuration(transformedTrek.duration_variations.variations[0].days);
      }
    } catch (error) {
      console.error('Error fetching trek:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get the selected variation
  const selectedVariation = trek?.duration_variations.variations.find(v => v.days === selectedDuration);
  // Get the itinerary to display (from variation if available, otherwise default)
  const displayItinerary = selectedVariation?.itinerary || trek?.itinerary;
  // Get the price to display (from variation if available, otherwise default)
  const displayPrice = selectedVariation?.price_usd || trek?.price_usd;

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!trek) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Trek Not Found</h1>
            <Link to="/treks">
              <Button>View All Treks</Button>
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-16 relative h-96 bg-gray-900">
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=600&fit=crop')`
          }}
        />
        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
          <div className="text-white">
            <Link to="/treks" className="inline-flex items-center text-white/80 hover:text-white mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Treks
            </Link>
            <h1 className="text-4xl sm:text-5xl font-bold mb-4" style={{ fontFamily: 'DM Serif Display, serif' }}>
              {trek.name}
            </h1>
            <p className="text-xl text-gray-200 mb-6">{trek.region}</p>
            <div className="flex items-center space-x-6 text-sm">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                <span>{selectedDuration || trek.minimum_days} days</span>
              </div>
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2" />
                <span>{trek.difficulty_level}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                <span>{trek.max_altitude}m max altitude</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Overview */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Overview</h2>
              <p className="text-gray-700 leading-relaxed">{trek.long_description}</p>
              {selectedVariation?.description && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-900 mb-2">{selectedDuration}-Day Trek Variation</h3>
                  <p className="text-blue-800">{selectedVariation.description}</p>
                </div>
              )}
            </div>

            {/* Itinerary */}
            {displayItinerary && displayItinerary.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Itinerary</h2>
                <div className="space-y-4">
                  {displayItinerary.map((day, index) => (
                    <div key={index} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                          {day.day}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{day.title}</h3>
                          {day.location && (
                            <div className="flex items-center text-gray-600 text-sm mb-2">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span>{day.location}</span>
                            </div>
                          )}
                          <p className="text-gray-600">{day.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Booking Card */}
            <div className="bg-white border rounded-xl p-6 shadow-lg sticky top-24">
              <div className="text-center mb-6">
                {displayPrice && (
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    ${displayPrice}
                    <span className="text-lg font-normal text-gray-600">/person</span>
                  </div>
                )}
                <div className="flex items-center justify-center text-yellow-400 mb-2">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 fill-current" />
                  ))}
                  <span className="text-sm text-gray-600 ml-2">4.9 (127 reviews)</span>
                </div>
              </div>

              <div className="space-y-4 mb-6">
                {trek.minimum_days && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Choose Duration</label>
                    <Select
                      value={selectedDuration?.toString()}
                      onValueChange={(value) => setSelectedDuration(parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select duration" />
                      </SelectTrigger>
                      <SelectContent>
                        {trek.duration_variations.variations.map((variation) => (
                          <SelectItem key={variation.days} value={variation.days.toString()}>
                            {variation.days} Days - ${variation.price_usd}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-gray-500">
                      Minimum duration: {trek.minimum_days} days
                    </p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Difficulty</span>
                  <span className="font-medium">{trek.difficulty_level}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Best Season</span>
                  <span className="font-medium">{trek.best_season}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Max Altitude</span>
                  <span className="font-medium">{trek.max_altitude}m</span>
                </div>
              </div>

              <Button 
                onClick={() => setIsBookingOpen(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 mb-4"
                size="lg"
              >
                Book This Trek
              </Button>

              <div className="text-center text-sm text-gray-600">
                💡 Visit our office in Thamel for the best deals!
              </div>
            </div>

            {/* Quick Facts */}
            <div className="bg-gradient-to-r from-blue-50 to-orange-50 rounded-xl p-6">
              <h3 className="font-bold text-gray-900 mb-4">Quick Facts</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-3 text-blue-600" />
                  <span>Group size: 2-12 people</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-3 text-blue-600" />
                  <span>Available year-round</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-3 text-blue-600" />
                  <span>No hidden costs</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
      <WhatsAppButton />
      
      <BookingModal 
        isOpen={isBookingOpen} 
        onClose={() => setIsBookingOpen(false)}
        trekName={trek.name}
        selectedDuration={selectedDuration}
      />
    </div>
  );
};

export default TrekDetail;
