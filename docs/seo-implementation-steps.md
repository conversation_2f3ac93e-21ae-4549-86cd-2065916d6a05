# SEO Implementation Guide for TrekNepalX

Here's a comprehensive guide to implement and optimize the SEO features we've built:

1. Database Setup & Data Migration
```sql
-- Run these in order in Supabase SQL editor
-- 1. Setup tables
\i supabase/migrations/20250706133202-add-seo-enhancement-tables.sql

-- 2. Initial data population
INSERT INTO schema_registry (page_url, schema_type, schema_data)
SELECT 
  '/treks/' || slug,
  'TrekPackage',
  jsonb_build_object(
    '@context', 'https://schema.org',
    '@type', 'TourPackage',
    'name', title,
    'description', description
  )
FROM treks;

-- 3. Setup image tracking
INSERT INTO image_seo_data (image_url, content_type, content_id)
SELECT 
  image_url,
  'trek',
  id
FROM trek_images;
```

2. Content Optimization Tasks
```bash
# Run initial freshness analysis
curl -X POST 'https://<project-ref>.functions.supabase.co/content-freshness-monitor' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -d '{"mode":"batch"}'

# Generate initial schemas
curl -X POST 'https://<project-ref>.functions.supabase.co/schema-generator' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -d '{"mode":"generate_all"}'

# Optimize existing images
curl -X POST 'https://<project-ref>.functions.supabase.co/image-seo-optimizer' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -d '{"mode":"batch"}'
```

3. Content Updates
- Review and update meta descriptions for all trek pages
- Implement structured data
- Optimize image alt texts
- Set up dynamic title tags

4. Component Integration

Add to TrekDetail.tsx:
```tsx
import { SchemaScript } from '@/components/SEO/SchemaScript';

export default function TrekDetail() {
  // ... existing code

  return (
    <>
      <SchemaScript 
        type="TrekPackage" 
        data={schemaData} 
      />
      {/* Rest of the component */}
    </>
  );
}
```

5. Critical Performance Optimization
```typescript
// Add to vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'trek-content': [
            './src/components/TrekDetail.tsx',
            './src/components/TrekItinerary.tsx'
          ],
          'admin': [
            './src/components/admin/ContentCalendar.tsx',
            './src/components/admin/SchemaManager.tsx'
          ]
        }
      }
    }
  }
});
```

6. Monitoring Setup
```sql
-- Create monitoring view
CREATE VIEW seo_health_metrics AS
SELECT 
  COUNT(DISTINCT s.id) as total_schemas,
  COUNT(DISTINCT i.id) as total_optimized_images,
  AVG(f.freshness_score) as avg_freshness,
  COUNT(DISTINCT CASE WHEN f.update_priority = 'high' THEN f.id END) as high_priority_updates
FROM schema_registry s
LEFT JOIN image_seo_data i ON i.optimization_status = 'optimized'
LEFT JOIN content_freshness f ON true;
```

7. Environment Configuration
```bash
# Add to .env
VITE_ENABLE_SEO_FEATURES=true
VITE_SEO_DEBUG_MODE=false
VITE_GEMINI_API_KEY=your_key
```

8. Next Steps for Dynamic Optimization:

a) Content Calendar Implementation:
```typescript
// Add to src/hooks/use-content-calendar.ts
export const useContentCalendar = () => {
  const generateTopics = async (existingContent: string[]) => {
    // Analyze existing content
    // Generate complementary topics
    // Consider seasonality
  };
  
  // ... rest of the hook
};
```

b) Automated Image Optimization:
```typescript
// Add to src/hooks/use-image-seo.ts
export const useImageSEO = () => {
  const batchOptimize = async (images: string[]) => {
    // Compress images
    // Generate alt text
    // Update metadata
  };
  
  // ... rest of the hook
};
```

c) Dynamic Schema Generation:
```typescript
// Add to src/hooks/use-schema-generator.ts
export const useSchemaGenerator = () => {
  const generateDynamicSchema = async (pageData: any) => {
    // Analyze page content
    // Generate appropriate schema
    // Validate schema
  };
  
  // ... rest of the hook
};
```

9. Testing & Validation:
```bash
# Run tests
npm run test:seo

# Validate schemas
curl -X POST 'https://<project-ref>.functions.supabase.co/schema-generator' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -d '{"mode":"validate_all"}'
```

10. Integration Points to Implement:

- Connect trek content updates to freshness monitoring
- Link image uploads to automatic SEO optimization
- Integrate schema generation into the publishing flow
- Set up automated content calendar suggestions

11. Regular Maintenance Tasks:

```sql
-- Clean up unused schemas
DELETE FROM schema_registry 
WHERE last_validated < NOW() - INTERVAL '30 days';

-- Archive old content suggestions
UPDATE content_calendar 
SET status = 'archived'
WHERE suggested_date < NOW() - INTERVAL '90 days';
```

Continue in a new chat for implementation assistance and debugging support.
