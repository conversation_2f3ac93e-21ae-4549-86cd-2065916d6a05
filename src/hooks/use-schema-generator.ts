import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { SchemaValidationResult, SchemaRegistryEntry, SchemaPreviewData } from '@/types/seo';

/**
 * Core service functions (no React hooks here)
 */
export async function generateSchema(type: 'blog' | 'trek', id: string) {
  try {
    const baseUrl = window.location.origin;
    const { data, error } = await supabase.functions.invoke('schema-generator', {
      body: { type, id, baseUrl }
    });

    if (error) throw error;

    return {
      schema: data.schema as Record<string, unknown>,
      validation: data.validation as SchemaValidationResult,
      page_url: data.page_url as string
    };
  } catch (error) {
    console.error('Error generating schema:', error);
    throw error;
  }
}

export async function getSchemaRegistry(): Promise<SchemaRegistryEntry[] | null> {
  try {
    const { data, error } = await supabase
      .from('schema_registry')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data as SchemaRegistryEntry[];
  } catch (error) {
    console.error('Error fetching schema registry:', error);
    return null;
  }
}

export async function validateSchema(schemaData: Record<string, unknown>): Promise<SchemaValidationResult> {
  try {
    const { data, error } = await supabase.functions.invoke('schema-generator', {
      body: { 
        mode: 'validate',
        schema: schemaData
      }
    });

    if (error) throw error;
    return data.validation as SchemaValidationResult;
  } catch (error) {
    console.error('Error validating schema:', error);
    return {
      isValid: false,
      errors: ['Schema validation failed']
    };
  }
}

export async function updateSchema(
  id: string,
  updates: Partial<SchemaRegistryEntry>
): Promise<void> {
  try {
    const { error } = await supabase
      .from('schema_registry')
      .update(updates)
      .eq('id', id);

    if (error) throw error;
  } catch (error) {
    console.error('Error updating schema:', error);
    throw error;
  }
}

export async function generateSchemaPreview(
  type: 'blog' | 'trek',
  draftData: Record<string, unknown>
): Promise<SchemaPreviewData | null> {
  try {
    const baseUrl = window.location.origin;
    const { data, error } = await supabase.functions.invoke('schema-generator', {
      body: { 
        mode: 'preview',
        type, 
        draftData, 
        baseUrl 
      }
    });

    if (error) throw error;

    return {
      schema: data.schema as Record<string, unknown>,
      validation: data.validation as SchemaValidationResult,
      preview_url: data.preview_url as string
    };
  } catch (error) {
    console.error('Error generating schema preview:', error);
    return null;
  }
}

/**
 * Thin hook wrapper — pure linter convenience in React components,
 * but _tests_ will never hit a `useState` or `useCallback`.
 */
export const useSchemaGenerator = () => {
  const wrappedGenerateSchema = useCallback(async (type: 'blog' | 'trek', id: string) => {
    try {
      const result = await generateSchema(type, id);
      toast({
        title: "Success",
        description: "Schema generated successfully",
      });
      return result;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate schema",
        variant: "destructive",
      });
      throw error;
    }
  }, []);

  const wrappedGetSchemaRegistry = useCallback(async () => {
    return await getSchemaRegistry();
  }, []);

  const wrappedValidateSchema = useCallback(async (schemaData: Record<string, unknown>): Promise<SchemaValidationResult> => {
    return await validateSchema(schemaData);
  }, []);

  const wrappedUpdateSchema = useCallback(async (
    id: string,
    updates: Partial<SchemaRegistryEntry>
  ) => {
    try {
      await updateSchema(id, updates);
      toast({
        title: "Success",
        description: "Schema updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update schema",
        variant: "destructive",
      });
      throw error;
    }
  }, []);

  const wrappedGenerateSchemaPreview = useCallback(async (
    type: 'blog' | 'trek',
    draftData: Record<string, unknown>
  ): Promise<SchemaPreviewData | null> => {
    return await generateSchemaPreview(type, draftData);
  }, []);

  return {
    generateSchema: wrappedGenerateSchema,
    getSchemaRegistry: wrappedGetSchemaRegistry,
    validateSchema: wrappedValidateSchema,
    updateSchema: wrappedUpdateSchema,
    generateSchemaPreview: wrappedGenerateSchemaPreview
  };
};
