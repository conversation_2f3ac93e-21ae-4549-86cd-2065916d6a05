import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { supabase } from '@/integrations/supabase/client';
import { generateSchema } from '@/hooks/use-schema-generator';
import { analyzeFreshness, batchAnalyzeFreshness, getFreshnessStats } from '@/hooks/use-content-freshness';
import { generateSuggestions, updateSuggestionStatus, getSuggestionsForMonth } from '@/hooks/use-content-calendar';
import { 
  mockSupabaseFunction, 
  mockSupabaseQuery, 
  mockSupabaseStorage,
  generateMockContentFreshnessData,
  generateMockSchemaRegistryEntry,
  generateMockContentSuggestion
} from '@/utils/test-utils';

describe('SEO Enhancements Integration Tests', () => {
  const testContentId = '123';
  const testUrl = 'https://example.com/test-image.jpg';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Content Freshness', () => {
    it('should analyze single content freshness', async () => {
      const mockData = generateMockContentFreshnessData({
        content_id: testContentId,
        freshness_score: 0.75
      });

      mockSupabaseFunction('content-freshness-monitor', mockData);

      const result = await analyzeFreshness(testContentId, 'blog');

      expect(result).toBeTruthy();
      expect(result?.freshness_score).toBe(0.75);
      expect(result?.content_id).toBe(testContentId);
    });

    it('should analyze batch content freshness', async () => {
      const mockData = [
        generateMockContentFreshnessData({ update_priority: 'high' }),
        generateMockContentFreshnessData({ update_priority: 'medium' })
      ];

      mockSupabaseFunction('content-freshness-monitor', mockData);

      const results = await batchAnalyzeFreshness('blog');

      expect(results).toHaveLength(2);
      expect(results.some(r => r.update_priority === 'high')).toBe(true);
    });

    it('should get freshness stats', async () => {
      const mockData = [
        generateMockContentFreshnessData({ freshness_score: 0.8, update_priority: 'high' }),
        generateMockContentFreshnessData({ freshness_score: 0.6, update_priority: 'medium' })
      ];

      mockSupabaseFunction('content-freshness-monitor', mockData);

      const stats = await getFreshnessStats('blog');

      expect(stats.averageFreshness).toBeCloseTo(0.7);
      expect(stats.highPriorityUpdates).toBe(1);
      expect(stats.totalContent).toBe(2);
    });
  });

  describe('Schema Generation', () => {
    it('should generate schema for content', async () => {
      const mockData = generateMockSchemaRegistryEntry({
        page_url: `/blog/${testContentId}`,
        validation_errors: []
      });

      mockSupabaseFunction('schema-generator', {
        schema: mockData.schema_data,
        validation: { isValid: true, errors: [] }
      });

      const result = await generateSchema('blog', testContentId);

      expect(result).toBeTruthy();
      expect(result.validation.isValid).toBe(true);
      expect(result.schema['@type']).toBe('BlogPosting');
    });
  });

  describe('Content Calendar', () => {
    it('should generate content suggestions', async () => {
      const mockData = {
        suggestions: [
          generateMockContentSuggestion({ seasonality_score: 0.9 }),
          generateMockContentSuggestion({ seasonality_score: 0.7 })
        ],
        suggestions_count: 2
      };

      mockSupabaseFunction('content-calendar-generator', mockData);

      const suggestions = await generateSuggestions();

      expect(suggestions).toHaveLength(2);
      expect(suggestions?.[0].seasonality_score).toBeGreaterThan(0.5);
    });

    it('should update suggestion status', async () => {
      const mockData = generateMockContentSuggestion({
        id: '789',
        status: 'approved'
      });

      mockSupabaseQuery('content_calendar', mockData);

      await updateSuggestionStatus('789', 'approved');

      // Verify the query was called with correct parameters
      expect(supabase.from).toHaveBeenCalledWith('content_calendar');
    });

    it('should get monthly suggestions', async () => {
      const mockData = [
        generateMockContentSuggestion({ suggested_date: new Date().toISOString() })
      ];

      mockSupabaseQuery('content_calendar', mockData);

      const suggestions = await getSuggestionsForMonth(
        new Date().getMonth(),
        new Date().getFullYear()
      );

      expect(suggestions).toBeTruthy();
      expect(Array.isArray(suggestions)).toBe(true);
    });
  });

  describe('Integration Scenarios', () => {
    it('should update freshness after content suggestion is published', async () => {
      // Mock content suggestion update
      const mockSuggestion = generateMockContentSuggestion({
        id: '999',
        status: 'published'
      });
      mockSupabaseQuery('content_calendar', mockSuggestion);

      // Mock freshness analysis after update
      const mockFreshness = generateMockContentFreshnessData({
        content_id: testContentId,
        freshness_score: 0.95
      });
      mockSupabaseFunction('content-freshness-monitor', mockFreshness);

      // Update suggestion status
      await updateSuggestionStatus('999', 'published');

      // Check freshness
      const freshness = await analyzeFreshness(testContentId, 'blog');
      expect(freshness?.freshness_score).toBe(0.95);
    });

    it('should generate schema with basic image data', async () => {
      const mockSchema = generateMockSchemaRegistryEntry({
        schema_data: {
          '@type': 'BlogPosting',
          image: testUrl
        }
      });
      mockSupabaseFunction('schema-generator', {
        schema: mockSchema.schema_data,
        validation: { isValid: true, errors: [] }
      });

      // Generate schema
      const schema = await generateSchema('blog', testContentId);
      expect(schema.schema.image).toBe(testUrl);
    });
  });
});
