# Frontend Implementation Guide for Supabase Functions

## Initial Setup

1. Setup Supabase Client with Types
```typescript
// src/integrations/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

export const supabase = createClient<Database>(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

// Add function caller utility
export const callFunction = async (
  functionName: string, 
  payload: any
) => {
  const { data, error } = await supabase.functions.invoke(functionName, {
    body: payload
  });
  
  if (error) throw error;
  return data;
};
```

## Content Freshness Implementation

1. Hook Integration:
```typescript
// src/hooks/use-content-freshness.ts
import { useCallback, useState } from 'react';
import { callFunction } from '@/integrations/supabase/client';

export const useContentFreshness = () => {
  const [loading, setLoading] = useState(false);

  const analyzeFreshness = useCallback(async (contentId: string) => {
    setLoading(true);
    try {
      return await callFunction('content-freshness-monitor', {
        contentId,
        mode: 'analyze'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  // Add hook to components
  return { analyzeFreshness, loading };
};
```

2. Component Integration:
```typescript
// src/components/TrekDetail.tsx
import { useContentFreshness } from '@/hooks/use-content-freshness';

export default function TrekDetail({ trekId }: { trekId: string }) {
  const { analyzeFreshness, loading } = useContentFreshness();

  useEffect(() => {
    analyzeFreshness(trekId);
  }, [trekId]);

  // ... rest of component
}
```

## Image SEO Implementation

1. Hook Setup:
```typescript
// src/hooks/use-image-seo.ts
export const useImageSEO = () => {
  const optimizeImage = useCallback(async (
    imageUrl: string,
    contentType: 'blog' | 'trek'
  ) => {
    return await callFunction('image-seo-optimizer', {
      imageUrl,
      contentType
    });
  }, []);

  // Add to image upload flow
  return { optimizeImage };
};
```

2. Component Integration:
```typescript
// src/components/ImageUpload.tsx
import { useImageSEO } from '@/hooks/use-image-seo';

export function ImageUpload() {
  const { optimizeImage } = useImageSEO();

  const handleUpload = async (file: File) => {
    // 1. Upload to storage
    const { data: imageData } = await supabase.storage
      .from('images')
      .upload(`${file.name}`, file);

    // 2. Optimize with SEO function
    if (imageData?.path) {
      const optimized = await optimizeImage(
        imageData.path,
        'trek'
      );
      
      // 3. Update metadata
      await supabase
        .from('images')
        .update({ metadata: optimized })
        .match({ path: imageData.path });
    }
  };
}
```

## Schema Generator Implementation

1. Hook Setup:
```typescript
// src/hooks/use-schema-generator.ts
export const useSchemaGenerator = () => {
  const generateSchema = useCallback(async (
    pageData: any,
    type: 'trek' | 'blog'
  ) => {
    return await callFunction('schema-generator', {
      pageData,
      type
    });
  }, []);

  return { generateSchema };
};
```

2. Integration with Pages:
```typescript
// src/components/SEO/SchemaScript.tsx
import { useSchemaGenerator } from '@/hooks/use-schema-generator';

export function SchemaScript({ pageData, type }: Props) {
  const { generateSchema } = useSchemaGenerator();
  const [schema, setSchema] = useState<string>('');

  useEffect(() => {
    const loadSchema = async () => {
      const generated = await generateSchema(pageData, type);
      setSchema(JSON.stringify(generated));
    };
    loadSchema();
  }, [pageData, type]);

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: schema }}
    />
  );
}
```

## Content Calendar Implementation

1. Hook Setup:
```typescript
// src/hooks/use-content-calendar.ts
export const useContentCalendar = () => {
  const generateSuggestions = useCallback(async () => {
    return await callFunction('content-calendar-generator', {
      mode: 'generate'
    });
  }, []);

  const updateStatus = useCallback(async (
    suggestionId: string,
    status: 'approved' | 'rejected'
  ) => {
    return await callFunction('content-calendar-generator', {
      mode: 'update',
      suggestionId,
      status
    });
  }, []);

  return { generateSuggestions, updateStatus };
};
```

2. Integration with Admin:
```typescript
// src/components/admin/ContentCalendar.tsx
import { useContentCalendar } from '@/hooks/use-content-calendar';

export function ContentCalendar() {
  const { generateSuggestions, updateStatus } = useContentCalendar();
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    const loadSuggestions = async () => {
      const data = await generateSuggestions();
      setSuggestions(data);
    };
    loadSuggestions();
  }, []);

  // ... rest of component
}
```

## Error Handling and Monitoring

1. Setup Error Boundary:
```typescript
// src/components/ErrorBoundary.tsx
export class SEOErrorBoundary extends React.Component {
  componentDidCatch(error: Error) {
    // Log to monitoring service
    console.error('SEO Function Error:', error);
  }

  render() {
    return this.props.children;
  }
}
```

2. Add Function Monitoring:
```typescript
// src/utils/monitoring.ts
export const trackFunctionExecution = async (
  functionName: string,
  duration: number,
  success: boolean
) => {
  await supabase
    .from('function_logs')
    .insert({
      function_name: functionName,
      execution_time: duration,
      success,
      timestamp: new Date()
    });
};
```

## Testing Function Integration

1. Setup Test Utils:
```typescript
// src/utils/test-utils.ts
export const mockSupabaseFunction = (
  functionName: string,
  mockResponse: any
) => {
  vi.spyOn(supabase.functions, 'invoke').mockImplementation(
    async (name) => {
      if (name === functionName) {
        return { data: mockResponse, error: null };
      }
      return { data: null, error: new Error('Not found') };
    }
  );
};
```

2. Add Integration Tests:
```typescript
// src/__tests__/function-integration.test.ts
describe('Function Integration', () => {
  it('should optimize images', async () => {
    const { optimizeImage } = useImageSEO();
    const result = await optimizeImage('test.jpg', 'trek');
    expect(result.alt_text).toBeDefined();
  });

  it('should generate schemas', async () => {
    const { generateSchema } = useSchemaGenerator();
    const result = await generateSchema({}, 'trek');
    expect(result['@type']).toBe('TrekPackage');
  });
});
```

## Usage in Next Steps

1. Start with database setup using `docs/seo-implementation-steps.md`
2. Implement the hooks and components from this guide
3. Test function integration
4. Monitor performance and errors
5. Continue to next chat for implementation assistance

Remember to:
- Handle errors gracefully
- Add loading states
- Cache function results where appropriate
- Monitor function execution times
- Set up retries for failed calls
