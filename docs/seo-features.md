# SEO Enhancement Features

## Overview

The SEO enhancement system provides automated tools and monitoring for improving search engine optimization across the platform. It includes:

1. Content Freshness Monitoring
2. Schema.org Implementation
3. Image SEO Optimization
4. Content Calendar Management

## Features

### Content Freshness Monitor

- Analyzes content age and relevance
- Suggests priority updates
- Tracks seasonal content
- Monitors content quality metrics
- Integrates with content calendar

```typescript
const { analyzeFreshness, getFreshnessStats } = useContentFreshness();
```

### Schema Generator

- Automatically generates JSON-LD schemas
- Supports multiple schema types:
  - BlogPosting
  - TrekPackage
  - FAQPage
  - LocalBusiness
- Validates schema structure
- Monitors schema coverage

```typescript
const { generateSchema, validateSchema } = useSchemaGenerator();
```

### Image SEO Optimizer

- Analyzes images using Gemini Vision API
- Generates optimized alt text
- Suggests SEO-friendly filenames
- Provides compression recommendations
- Tracks optimization metrics

```typescript
const { optimizeImage, getImageSEOStats } = useImageSEO();
```

### Content Calendar

- AI-powered content suggestions
- Seasonal content planning
- Engagement predictions
- Publishing schedule management
- Integration with freshness monitoring

```typescript
const { generateSuggestions, getCalendarStats } = useContentCalendar();
```

## Directory Structure

```
src/
├── hooks/
│   ├── use-content-freshness.ts
│   ├── use-schema-generator.ts
│   ├── use-image-seo.ts
│   └── use-content-calendar.ts
├── components/admin/
│   ├── ContentFreshnessMonitor.tsx
│   ├── SchemaManager.tsx
│   ├── ImageSEOManager.tsx
│   └── ContentCalendar.tsx
└── types/
    └── seo.ts
```

## API Reference

### Content Freshness Analysis

```typescript
interface ContentFreshnessData {
  freshness_score: number;          // 0-1 score
  update_priority: 'low' | 'medium' | 'high';
  suggested_updates: Array<{
    section: string;
    reason: string;
    suggestion: string;
  }>;
  seasonal_relevance: boolean;
  next_review_date: string;
}
```

### Schema Registry

```typescript
interface SchemaRegistryEntry {
  id: string;
  page_url: string;
  schema_type: 'BlogPosting' | 'TrekPackage' | 'FAQPage' | 'LocalBusiness';
  schema_data: Record<string, unknown>;
  validation_errors: string[];
  auto_update: boolean;
}
```

### Image SEO Data

```typescript
interface ImageSEOData {
  id: string;
  image_url: string;
  alt_text: string | null;
  suggested_filename: string | null;
  vision_api_tags: string[];
  optimization_status: 'pending' | 'optimized';
  compression_needed: boolean;
}
```

## Usage Examples

### Analyzing Content Freshness

```typescript
const analysis = await analyzeFreshness('blog', postId);
if (analysis.update_priority === 'high') {
  // Schedule content update
}
```

### Generating Schemas

```typescript
const schema = await generateSchema('trek', trekId);
if (schema.validation.isValid) {
  // Add schema to page
}
```

### Optimizing Images

```typescript
const result = await optimizeImage(imageUrl, 'blog', postId);
if (result) {
  await updateImageMetadata(imageId, {
    alt: result.alt_text,
    filename: result.suggested_filename
  });
}
```

### Managing Content Calendar

```typescript
const suggestions = await generateSuggestions();
const nextMonth = await getSuggestionsForMonth(
  currentDate.getMonth() + 1,
  currentDate.getFullYear()
);
```

## Testing

Run the integration tests:

```bash
npm run test:seo
```

This will test all SEO enhancement features including:
- Schema generation and validation
- Content freshness analysis
- Image optimization
- Content calendar functionality

## Contributing

When adding new SEO features:

1. Update shared types in `src/types/seo.ts`
2. Add corresponding Edge Functions if needed
3. Create React hooks for the feature
4. Add admin UI components
5. Include integration tests
6. Update this documentation
