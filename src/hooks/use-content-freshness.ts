import { useCallback, useState } from 'react';
import { callFunction } from '@/integrations/supabase/client';
import type { ContentFreshnessData } from '@/types/seo';

type ContentType = 'blog' | 'trek';

interface ContentFreshnessPayload {
  [key: string]: unknown;
  contentId: string;
  contentType: ContentType;
  mode: 'analyze' | 'batch';
}

interface FreshnessStats {
  totalContent: number;
  needsUpdate: number;
  averageFreshness: number;
  highPriorityUpdates: number;
  priorityUpdates: ContentFreshnessData[];
  seasonalContent: ContentFreshnessData[];
}

/**
 * Core service functions (no React hooks here)
 */
export async function analyzeFreshness(
  contentId: string,
  contentType: 'blog' | 'trek'
): Promise<ContentFreshnessData | null> {
  try {
    const payload: ContentFreshnessPayload = {
      contentId,
      contentType,
      mode: 'analyze'
    };

    const data = await callFunction<ContentFreshnessData>(
      'content-freshness-monitor',
      payload
    );
    
    return data;
  } catch (err) {
    console.error('analyzeFreshness error:', err);
    return null;
  }
}

export async function batchAnalyzeFreshness(
  contentType: 'blog' | 'trek'
): Promise<ContentFreshnessData[]> {
  try {
    const payload: ContentFreshnessPayload = {
      contentId: '', // Not needed for batch mode
      contentType,
      mode: 'batch'
    };

    const data = await callFunction<ContentFreshnessData[]>(
      'content-freshness-monitor',
      payload
    );
    
    return data;
  } catch (err) {
    console.error('batchAnalyzeFreshness error:', err);
    return [];
  }
}

export async function getFreshnessStats(
  contentType: ContentType = 'trek'
): Promise<FreshnessStats> {
  try {
    const freshnessData = await batchAnalyzeFreshness(contentType);
    
    const highPriorityUpdates = freshnessData.filter(d => d.update_priority === 'high');
    const priorityUpdates = freshnessData.filter(d => d.update_priority !== 'low');
    const seasonalContent = freshnessData.filter(d => d.seasonal_relevance);
    
    return {
      totalContent: freshnessData.length,
      needsUpdate: priorityUpdates.length,
      averageFreshness: freshnessData.reduce((acc, curr) => acc + curr.freshness_score, 0) / freshnessData.length,
      highPriorityUpdates: highPriorityUpdates.length,
      priorityUpdates,
      seasonalContent
    };
  } catch (err) {
    console.error('getFreshnessStats error:', err);
    return {
      totalContent: 0,
      needsUpdate: 0,
      averageFreshness: 0,
      highPriorityUpdates: 0,
      priorityUpdates: [],
      seasonalContent: []
    };
  }
}

/**
 * Thin hook wrapper — pure linter convenience in React components,
 * but _tests_ will never hit a `useState` or `useCallback`.
 */
export const useContentFreshness = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const wrappedAnalyzeFreshness = useCallback(async (
    contentId: string,
    contentType: 'blog' | 'trek'
  ): Promise<ContentFreshnessData | null> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await analyzeFreshness(contentId, contentType);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to analyze content freshness'));
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const wrappedBatchAnalyzeFreshness = useCallback(async (
    contentType: 'blog' | 'trek'
  ): Promise<ContentFreshnessData[]> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await batchAnalyzeFreshness(contentType);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to run batch freshness analysis'));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  const wrappedGetFreshnessStats = useCallback(async (
    contentType: ContentType = 'trek'
  ): Promise<FreshnessStats> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await getFreshnessStats(contentType);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to get freshness stats'));
      return {
        totalContent: 0,
        needsUpdate: 0,
        averageFreshness: 0,
        highPriorityUpdates: 0,
        priorityUpdates: [],
        seasonalContent: []
      };
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    analyzeFreshness: wrappedAnalyzeFreshness,
    batchAnalyzeFreshness: wrappedBatchAnalyzeFreshness,
    getFreshnessStats: wrappedGetFreshnessStats,
    loading,
    error
  };
};
